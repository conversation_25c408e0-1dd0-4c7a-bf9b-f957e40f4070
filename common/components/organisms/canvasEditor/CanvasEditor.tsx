'use client'

import React, {
  useState, useRef, useEffect,
  useCallback,
} from 'react';
import * as fabric from 'fabric';
import { 
  CanvasSidebar, CanvasToolbar, SaveLoadModal,
} from '@/common/components/organisms';
import { cn } from '@/common/utils/helpers';

interface CanvasEditorProps {
  isOpen: boolean;
  onClose: () => void;
  onSave?: (imageUrl: string) => void;
  initialImage?: string;
  className?: string;
  agentId: string;
  planId: string;
}

export const CanvasEditor = ({
  isOpen,
  onClose,
  onSave,
  initialImage,
  className,
  agentId,
  planId,
}: CanvasEditorProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [fabricCanvas, setFabricCanvas] = useState<fabric.Canvas | null>(null);
  const [canvasHistory, setCanvasHistory] = useState<string[]>([]);
  const [historyIndex, setHistoryIndex] = useState(-1);
  const [isSaveModalOpen, setIsSaveModalOpen] = useState(false);

  const saveCanvasStateRef = useRef<(canvas: fabric.Canvas) => void>();
  const undoRef = useRef<() => void>();
  const redoRef = useRef<() => void>();
  const isUndoRedoInProgressRef = useRef(false);

  saveCanvasStateRef.current = (canvas: fabric.Canvas) => {
    // Don't save state during undo/redo operations
    if (isUndoRedoInProgressRef.current) {
      return;
    }

    const state = JSON.stringify(canvas.toJSON());
    setHistoryIndex(prevIndex => {
      setCanvasHistory(prev => {
        const newHistory = prev.slice(0, prevIndex + 1);
        newHistory.push(state);
        return newHistory;
      });
      return prevIndex + 1;
    });
  };

  undoRef.current = () => {
    if (historyIndex > 0 && fabricCanvas) {
      isUndoRedoInProgressRef.current = true;
      const prevState = canvasHistory[historyIndex - 1];
      fabricCanvas.loadFromJSON(prevState, () => {
        fabricCanvas.renderAll();
        setHistoryIndex(prev => prev - 1);
        // Use setTimeout to ensure all events have finished processing
        setTimeout(() => {
          isUndoRedoInProgressRef.current = false;
        }, 0);
      });
    }
  };

  redoRef.current = () => {
    if (historyIndex < canvasHistory.length - 1 && fabricCanvas) {
      isUndoRedoInProgressRef.current = true;
      const nextState = canvasHistory[historyIndex + 1];
      fabricCanvas.loadFromJSON(nextState, () => {
        fabricCanvas.renderAll();
        setHistoryIndex(prev => prev + 1);
        // Use setTimeout to ensure all events have finished processing
        setTimeout(() => {
          isUndoRedoInProgressRef.current = false;
        }, 0);
      });
    }
  };

  const saveCanvasState = useCallback((canvas: fabric.Canvas) => {
    saveCanvasStateRef.current?.(canvas);
  }, []);

  const undo = useCallback(() => {
    undoRef.current?.();
  }, []);

  const redo = useCallback(() => {
    redoRef.current?.();
  }, []);

  useEffect(() => {
    if (!canvasRef.current || !isOpen) {
      return;
    }
    const isMobile = window.innerWidth < 768;
    const canvasWidth = isMobile ? Math.min(window.innerWidth - 32, 600) : 800;
    const canvasHeight = isMobile ? Math.min(window.innerHeight - 300, 400) : 600;

    const canvas = new fabric.Canvas(canvasRef.current, {
      width: canvasWidth,
      height: canvasHeight,
      backgroundColor: '#ffffff',
      selection: true,
      preserveObjectStacking: true,
    });

    canvas.on('mouse:wheel', function(opt) {
      const delta = opt.e.deltaY;
      let zoom = canvas.getZoom();
      zoom *= 0.999 ** delta;
      if (zoom > 20) {
        zoom = 20;
      }
      if (zoom < 0.01) {
        zoom = 0.01;
      }
      canvas.zoomToPoint(new fabric.Point(opt.e.offsetX, opt.e.offsetY), zoom);
      opt.e.preventDefault();
      opt.e.stopPropagation();
    });

    // Pan functionality
    canvas.on('mouse:down', function (this: any, opt: any) {
      const evt = opt.e;
      if (evt.altKey === true) {
        this.isDragging = true;
        this.selection = false;
        this.lastPosX = evt.clientX;
        this.lastPosY = evt.clientY;
      }
    });

    canvas.on('mouse:move', function (this: any, opt: any) {
      if (this.isDragging) {
        const e = opt.e;
        const vpt = this.viewportTransform;
        vpt[4] += e.clientX - this.lastPosX;
        vpt[5] += e.clientY - this.lastPosY;
        this.requestRenderAll();
        this.lastPosX = e.clientX;
        this.lastPosY = e.clientY;
      }
    });

    canvas.on('mouse:up', function(this: any, _opt: any) {
      this.setViewportTransform(this.viewportTransform);
      this.isDragging = false;
      this.selection = true;
    });

    // Touch support for mobile devices
    let lastTouchDistance = 0;

    // @ts-ignore - Touch events not in types
    canvas.on('touch:gesture', function(e: any) {
      if (e.e.touches && e.e.touches.length === 2) {
        const touch1 = e.e.touches[0];
        const touch2 = e.e.touches[1];

        const distance = Math.sqrt(
          Math.pow(touch2.clientX - touch1.clientX, 2) +
          Math.pow(touch2.clientY - touch1.clientY, 2)
        );

        const center = {
          x: (touch1.clientX + touch2.clientX) / 2,
          y: (touch1.clientY + touch2.clientY) / 2
        };

        if (lastTouchDistance > 0) {
          const scale = distance / lastTouchDistance;
          let zoom = canvas.getZoom() * scale;

          if (zoom > 20) zoom = 20;
          if (zoom < 0.01) zoom = 0.01;

          canvas.zoomToPoint(new fabric.Point(center.x, center.y), zoom);
        }

        lastTouchDistance = distance;
        e.e.preventDefault();
      }
    });

    // @ts-ignore - Touch events not in types
    canvas.on('touch:drag', function(this: any, e: any) {
      if (e.e.touches && e.e.touches.length === 1) {
        // Single finger drag for panning
        const touch = e.e.touches[0];
        if (this.lastTouchX && this.lastTouchY) {
          const vpt = this.viewportTransform;
          vpt[4] += touch.clientX - this.lastTouchX;
          vpt[5] += touch.clientY - this.lastTouchY;
          this.requestRenderAll();
        }
        this.lastTouchX = touch.clientX;
        this.lastTouchY = touch.clientY;
      }
    });

    // @ts-ignore - Touch events not in types
    canvas.on('touch:longpress', function(e: any) {
      // Long press to select objects on mobile
      const target = canvas.findTarget(e.e);
      if (target) {
        canvas.setActiveObject(target);
        canvas.renderAll();
      }
    });

    // Set up canvas event listeners for history
    canvas.on('object:modified', () => {
      saveCanvasState(canvas);
    });

    canvas.on('object:added', () => {
      saveCanvasState(canvas);
    });

    canvas.on('object:removed', () => {
      saveCanvasState(canvas);
    });

    // Keyboard shortcuts functionality
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Delete') {
        const activeObjects = canvas.getActiveObjects();
        if (activeObjects.length) {
          activeObjects.forEach(obj => canvas.remove(obj));
          canvas.discardActiveObject();
          canvas.renderAll();
        }
      }

      // Undo/Redo shortcuts
      if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {
        e.preventDefault();
        undoRef.current?.();
      }

      if ((e.ctrlKey || e.metaKey) && (e.key === 'y' || (e.key === 'z' && e.shiftKey))) {
        e.preventDefault();
        redoRef.current?.();
      }
    };

    document.addEventListener('keydown', handleKeyDown);

    setFabricCanvas(canvas);

    // Load initial image if provided
    if (initialImage) {
      fabric.FabricImage.fromURL(initialImage).then((img: any) => {
        img.scaleToWidth(400);
        // Manually center the image
        img.set({
          left: (canvas.width! - img.getScaledWidth()) / 2,
          top: (canvas.height! - img.getScaledHeight()) / 2,
        });
        canvas.add(img);
        canvas.renderAll();
        // Save initial state after image is loaded
        saveCanvasState(canvas);
      });
    } else {
      // Save initial state for blank canvas
      saveCanvasState(canvas);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      canvas.dispose();
    };
  }, [isOpen, initialImage, saveCanvasState]);

  const handleSaveDesign = () => {
    setIsSaveModalOpen(true);
  };

  if (!isOpen) {
    return null;
  }
  return (
    <div className={cn(
      "fixed z-50 top-[60px] left-0 right-0 bottom-0 h-[calc(100vh-60px)] bg-neutral-900 flex flex-col",
      className,
    )}>
      <CanvasToolbar
        onClose={onClose}
        onSaveDesign={handleSaveDesign}
        onUndo={undo}
        onRedo={redo}
        canUndo={historyIndex > 0}
        canRedo={historyIndex < canvasHistory.length - 1}
      />

      <div className="flex flex-1 min-h-0 flex-col md:flex-row">
        <div className="block">
          <CanvasSidebar
            canvas={fabricCanvas}
            agentId={agentId}
            planId={planId}
          />
        </div>
        <div className="flex-1 flex flex-col items-center justify-center bg-neutral-800 p-2 md:p-4">
          <div className="bg-white rounded-xl shadow-2xl p-2 md:p-4 w-auto">
            <canvas
              ref={canvasRef}
              className="border border-gray-200 rounded w-full max-w-full"
              style={{ maxHeight: 'calc(100vh - 200px)' }}
            />
          </div>
          <div className="mt-2 md:mt-4 text-center text-gray-500 text-xs md:text-sm px-2">
            <p className="hidden md:block">Mouse wheel to zoom | Alt + drag to pan | Delete key to remove selected objects</p>
            <p className="hidden md:block text-xs">Ctrl+Z to undo | Ctrl+Y or Ctrl+Shift+Z to redo</p>
          </div>
        </div>
      </div>

      <SaveLoadModal
        isOpen={isSaveModalOpen}
        onClose={() => setIsSaveModalOpen(false)}
        canvas={fabricCanvas}
        mode="save"
      />
    </div>
  );
};
